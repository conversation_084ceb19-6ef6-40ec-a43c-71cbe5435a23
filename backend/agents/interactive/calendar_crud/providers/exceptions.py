"""
Exceptions for calendar providers.

This module defines exception classes for calendar provider operations,
providing specific error types for different failure scenarios.
"""

from typing import Optional

class CalendarProviderError(Exception):
    """
    Base exception for calendar provider errors.

    This is the parent class for all calendar provider exceptions.
    It can be used to catch any calendar provider error.
    """
    pass

class AuthenticationError(CalendarProviderError):
    """
    Exception raised when authentication fails.

    This exception is raised when the provider rejects the authentication
    credentials, such as when an OAuth token is invalid or expired.
    """
    pass

class ProviderAuthError(CalendarProviderError):
    """
    Exception raised when auth-service returns non-200 response.

    This exception is raised when the auth-service returns 404/401
    or other non-200 status codes when fetching tokens.
    """
    pass

class AuthClientError(CalendarProviderError):
    """
    Base exception for auth client errors.

    This is the parent class for auth client specific exceptions.
    """
    pass

class AuthRateLimit(AuthClientError):
    """
    Exception raised when auth-service returns 429 (rate limit).

    This exception is raised when the auth-service is rate limiting
    requests and should be retried with exponential backoff.
    """
    pass

class AuthUnavailable(AuthClientError):
    """
    Exception raised when auth-service returns 5xx errors.

    This exception is raised when the auth-service is temporarily
    unavailable and should be retried with exponential backoff.
    """
    pass

class ResourceNotFoundError(CalendarProviderError):
    """
    Exception raised when a resource is not found.

    This exception is raised when a requested resource (calendar, event, etc.)
    does not exist or is not accessible to the authenticated user.
    """
    pass

class PermissionError(CalendarProviderError):
    """
    Exception raised when the user doesn't have permission.

    This exception is raised when the authenticated user does not have
    sufficient permissions to perform the requested operation.
    """
    pass

class RateLimitError(CalendarProviderError):
    """
    Exception raised when the provider's rate limit is exceeded.

    This exception is raised when the provider rejects a request due to
    rate limiting, such as when too many requests are made in a short period.

    Attributes:
        retry_after: Seconds to wait before retrying (from provider)
    """
    def __init__(self, message: str, retry_after: Optional[int] = None) -> None:
        """
        Initialize a rate limit error.

        Args:
            message: Error message
            retry_after: Seconds to wait before retrying (from provider)
        """
        super().__init__(message)
        self.retry_after = retry_after

class ValidationError(CalendarProviderError):
    """
    Exception raised when input validation fails.

    This exception is raised when the input parameters for an operation
    are invalid or incomplete.
    """
    pass

class WebhookError(CalendarProviderError):
    """
    Exception raised when there's an error with webhooks.

    This exception is raised when there's an error setting up, updating,
    or processing webhooks for calendar events.
    """
    pass

class ConfigurationError(CalendarProviderError):
    """
    Exception raised when there's a configuration error.

    This exception is raised when there's an error in the provider's
    configuration, such as missing required settings.
    """
    pass

class NetworkError(CalendarProviderError):
    """
    Exception raised when there's a network error.

    This exception is raised when there's an error communicating with
    the provider's API due to network issues.
    """
    pass

class ServiceUnavailableError(CalendarProviderError):
    """
    Exception raised when the provider's service is unavailable.

    This exception is raised when the provider's service is temporarily
    unavailable or experiencing issues.
    """
    pass

class UnsupportedOperationError(CalendarProviderError):
    """
    Exception raised when an operation is not supported by the provider.

    This exception is raised when a client attempts to perform an operation
    that is not supported by the provider, such as creating an event with
    a provider that only supports read operations.
    """
    pass
