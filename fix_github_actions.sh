
#!/bin/bash
# <PERSON><PERSON><PERSON> to fix GitHub Actions workflow versions
# Run this script from the repository root

echo "🔧 Fixing GitHub Actions workflow versions..."

# Update actions/checkout@v3 to @v4
find .github/workflows -name "*.yml" -exec sed -i '' 's/actions\/checkout@v3/actions\/checkout@v4/g' {} \;

# Update actions/setup-python@v4 to @v5  
find .github/workflows -name "*.yml" -exec sed -i '' 's/actions\/setup-python@v4/actions\/setup-python@v5/g' {} \;

# Update actions/upload-artifact@v3 to @v4
find .github/workflows -name "*.yml" -exec sed -i '' 's/actions\/upload-artifact@v3/actions\/upload-artifact@v4/g' {} \;

echo "✅ GitHub Actions workflow versions updated!"
echo ""
echo "📋 Next steps:"
echo "1. Review the changes: git diff .github/workflows/"
echo "2. Commit the changes: git add .github/workflows/ && git commit -m 'fix: update GitHub Actions to latest versions'"
echo "3. Push to PR branch: git push"
echo ""
echo "This will trigger GitHub to re-run the checks with the updated action versions."
