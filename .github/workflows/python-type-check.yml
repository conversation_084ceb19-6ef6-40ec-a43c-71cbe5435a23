name: Python Type Check

on:
  push:
    branches: [main, dev]
    paths:
      - 'pi_lawyer/**/*.py'
      - 'pyproject.toml'
  pull_request:
    branches: [main, dev]
    paths:
      - 'pi_lawyer/**/*.py'
      - 'pyproject.toml'

jobs:
  type-check:
    name: Python Type Check
    runs-on: ubuntu-latest

    env:
      SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL || 'dummy' }}
      SUPABASE_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy' }}
      PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY || 'dummy' }}
      PINECONE_ENVIRONMENT: us-east-1
      PINECONE_INDEX_NAME: new-texas-laws
      GCS_BUCKET_NAME: texas-laws-personalinjury
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY || 'dummy' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install pydantic mypy black isort
          pip install -r requirements.txt

      - name: Run Black (formatting check)
        run: black --check pi_lawyer/

      - name: Run MyPy (type check)
        run: mypy --config-file pyproject.toml pi_lawyer/
