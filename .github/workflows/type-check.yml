name: Type Check & Format

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  typecheck:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.9'
          cache: 'pip'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install mypy black

      - name: Check code formatting
        run: |
          python -m black --check pi_lawyer

      - name: Run mypy
        run: |
          python -m mypy pi_lawyer --show-error-codes
