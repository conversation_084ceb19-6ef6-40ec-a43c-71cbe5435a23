name: TypeScript Type Check

on:
  push:
    branches: [main, dev]
    paths:
      - 'frontend/**/*.ts'
      - 'frontend/**/*.tsx'
      - 'frontend/tsconfig*.json'
      - '.github/workflows/**'  # Run when workflows change
  pull_request:
    branches: [main, dev, final-auth-checks]
    paths:
      - 'frontend/**/*.ts'
      - 'frontend/**/*.tsx'
      - 'frontend/tsconfig*.json'
      - '.github/workflows/**'  # Run when workflows change

jobs:
  type-check:
    name: TypeScript Type Check
    runs-on: ubuntu-latest

    env:
      NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL || 'dummy' }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy' }}
      SUPABASE_ACCESS_TOKEN: ${{ secrets.SUPABASE_ACCESS_TOKEN || 'dummy' }}
      SUPABASE_JWT_SECRET: ${{ secrets.SUPABASE_JWT_SECRET || 'dummy' }}
      PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY || 'dummy' }}
      PINECONE_ENVIRONMENT: us-east-1
      PINECONE_INDEX_NAME: new-texas-laws
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY || 'dummy' }}
      NEXT_PUBLIC_TURNSTILE_SITE_KEY: ${{ secrets.NEXT_PUBLIC_TURNSTILE_SITE_KEY || 'dummy' }}
      TURNSTILE_SECRET_KEY: ${{ secrets.TURNSTILE_SECRET_KEY || 'dummy' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: 'frontend/package-lock.json'

      - name: Install dependencies
        run: cd frontend && npm ci

      - name: Run TypeScript type check
        run: cd frontend && npm run type-check

      - name: Run strict TypeScript type check (informational)
        run: cd frontend && npm run type-check:strict
        continue-on-error: true
