name: Test Coverage

on:
  push:
    branches: [ main, py-agent ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    env:
      SUPABASE_URL: ${{ secrets.SUPABASE_URL || 'dummy' }}
      SUPABASE_KEY: ${{ secrets.SUPABASE_ANON_KEY || 'dummy' }}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'dummy' }}
      PINECONE_API_KEY: ${{ secrets.PINECONE_API_KEY || 'dummy' }}
      PINECONE_ENVIRONMENT: us-east-1
      PINECONE_INDEX_NAME: new-texas-laws
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY || 'dummy' }}

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.10'
        cache: 'pip'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        pip install pytest pytest-cov pytest-asyncio coveragepy

    - name: Run tests with coverage for services
      run: |
        python -m pytest tests/test_circuit_breaker.py tests/test_error_classification.py --cov=pi_lawyer.services.circuit_breaker --cov=pi_lawyer.services.error_classification --cov-report=xml

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        file: ./coverage.xml
        flags: unittests
        fail_ci_if_error: false

    - name: Generate coverage badge
      if: github.event_name != 'pull_request'
      run: |
        python -c "
        import xml.etree.ElementTree as ET
        tree = ET.parse('coverage.xml')
        root = tree.getroot()
        coverage = float(root.get('line-rate')) * 100
        color = 'red' if coverage < 70 else 'yellow' if coverage < 80 else 'green'
        print(f'Coverage: {coverage:.2f}%')
        with open('coverage_badge.json', 'w') as f:
          f.write('{')
          f.write(f'\"schemaVersion\": 1, \"label\": \"coverage\", \"message\": \"{coverage:.2f}%\", \"color\": \"{color}\"')
          f.write('}')
        "

    - name: Check coverage threshold
      run: |
        python -c "
        import xml.etree.ElementTree as ET
        tree = ET.parse('coverage.xml')
        root = tree.getroot()
        coverage = float(root.get('line-rate')) * 100
        threshold = 80.0
        print(f'Coverage: {coverage:.2f}%')
        print(f'Threshold: {threshold}%')
        if coverage < threshold:
          print('Coverage below threshold')
          exit(1)
        else:
          print('Coverage above threshold')
        "
