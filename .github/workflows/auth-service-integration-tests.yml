name: Auth-Service Integration Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'backend/agents/interactive/calendar_crud/**'
      - 'backend/config.py'
      - 'tests/integration/test_auth_service_flow.py'
      - 'tests/integration/run_auth_service_tests.py'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'backend/agents/interactive/calendar_crud/**'
      - 'backend/config.py'
      - 'tests/integration/test_auth_service_flow.py'
      - 'tests/integration/run_auth_service_tests.py'

jobs:
  auth-service-integration-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version: [3.9, 3.10, 3.11]

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Install test dependencies
      run: |
        python -m pip install --upgrade pip
        pip install pytest pytest-asyncio pytest-timeout httpx respx

    - name: Run Auth-Service Integration Tests
      run: |
        python tests/integration/run_auth_service_tests.py
      env:
        # Note: AUTH_SERVICE_BASE is intentionally not set to test
        # that the tests work without real service dependencies
        PYTHONPATH: .

    - name: Upload test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: auth-service-test-results-${{ matrix.python-version }}
        path: |
          tests/integration/test_auth_service_flow.py
          tests/integration/run_auth_service_tests.py
        retention-days: 30
