name: CI

on:
  push:
    branches: [ main ]
  pull_request:

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test
        ports: ['5432:5432']
    env:
      SUPABASE_URL: http://dummy.local
      SUPABASE_KEY: dummy
      OPENAI_API_KEY: dummy
      PINECONE_API_KEY: dummy
      PINECONE_ENVIRONMENT: dev
      PINECONE_INDEX_NAME: dummy
      DB_USER: test
      DB_PASSWORD: test
      DB_NAME: test
      DB_HOST: localhost
      DB_PORT: 5432
      PROMPT_MGR_URL: http://localhost:8000/prompt-mgr/api/v1

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python 3.10
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
          cache: 'pip'

      - name: Set up Node 20
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Run unified setup & tests
        run: ./setup.sh

      - name: Run Prompt Manager tests
        run: |
          pip install pytest pytest-asyncio httpx sqlalchemy aiosqlite pydantic
          pytest services/prompt_mgr/tests/

