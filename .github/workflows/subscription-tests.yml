name: Subscription Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'src/components/subscription/**'
      - 'src/lib/services/**'
      - 'cypress/e2e/subscription/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'src/components/subscription/**'
      - 'src/lib/services/**'
      - 'cypress/e2e/subscription/**'

jobs:
  unit-tests:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run component tests
        run: npm run test:subscription

      - name: Run service tests
        run: npm run test:subscription:service

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: unit-test-results
          path: coverage/

  e2e-tests:
    runs-on: ubuntu-latest
    needs: unit-tests

    steps:
      - uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Cypress
        run: npx cypress install

      - name: Run E2E tests
        uses: cypress-io/github-action@v5
        with:
          browser: chrome
          spec: cypress/e2e/subscription/**/*.cy.ts

      - name: Upload screenshots
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: cypress-screenshots
          path: cypress/screenshots

      - name: Upload videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos
          path: cypress/videos
