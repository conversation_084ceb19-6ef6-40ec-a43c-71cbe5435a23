"""
Auth client wrapper with retry logic for auth-service outages.

This module provides a wrapper around auth-service token fetching with
exponential backoff retry logic to handle rate limits and service outages
gracefully.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from tenacity import (
    retry,
    wait_exponential,
    stop_after_attempt,
    retry_if_exception_type,
    before_sleep_log
)

from backend.config import settings
from shared.http import SharedHTTPClient, HTTPNonRetryableError, HTTPRetryableError, HTTPClientError
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    AuthClientError,
    AuthRateLimit,
    AuthUnavailable,
    ProviderAuthError,
    AuthenticationError
)

# Configure logging
logger = logging.getLogger(__name__)

# In-memory token cache with TTL (shared across providers)
_token_cache: Dict[str, Dict[str, Any]] = {}

# Metrics collector instance (lazy loaded)
_metrics_collector = None


def _get_metrics_collector():
    """Get or create metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        try:
            from src.pi_lawyer.services.metrics_collector import MetricsCollector
            _metrics_collector = MetricsCollector(prefix="auth_client")
        except ImportError:
            # Fallback if metrics collector is not available
            logger.warning("MetricsCollector not available, metrics will not be collected")
            _metrics_collector = None
    return _metrics_collector


def _increment_retry_metric(provider: str):
    """Increment retry counter metric."""
    metrics = _get_metrics_collector()
    if metrics:
        metrics.increment_counter("auth_retry_total", tags={"provider": provider})


def _increment_failure_metric(provider: str):
    """Increment failure counter metric."""
    metrics = _get_metrics_collector()
    if metrics:
        metrics.increment_counter("auth_failure_total", tags={"provider": provider})


def _map_http_error_to_auth_error(error: Exception) -> Exception:
    """
    Map HTTP errors to appropriate auth client exceptions.
    
    Args:
        error: The original HTTP error
        
    Returns:
        Mapped auth client exception
    """
    if isinstance(error, HTTPNonRetryableError):
        error_str = str(error)
        if "429" in error_str:
            return AuthRateLimit(f"Auth service rate limit: {error}")
        else:
            # Other 4xx errors should not be retried
            return ProviderAuthError(f"Auth service error: {error}")
    elif isinstance(error, (HTTPRetryableError, HTTPClientError)):
        # 5xx errors and network errors should be retried
        return AuthUnavailable(f"Auth service unavailable: {error}")
    else:
        # Unknown errors
        return AuthenticationError(f"Unexpected auth error: {error}")


@retry(
    wait=wait_exponential(multiplier=0.5, max=8),
    stop=stop_after_attempt(4),
    retry=retry_if_exception_type((AuthRateLimit, AuthUnavailable)),
    before_sleep=before_sleep_log(logger, logging.WARNING)
)
async def get_access_token_with_retry(firm_id: str, provider: str) -> str:
    """
    Get an access token for the specified provider with retry logic.
    
    This function implements exponential backoff retry logic for handling
    auth-service outages and rate limits. It will retry up to 4 times
    with exponential backoff (0.5s, 1s, 2s, 4s) for a maximum wait of ~7.5s.
    
    Args:
        firm_id: The firm/tenant ID
        provider: The provider name (e.g., 'google', 'calendly')
        
    Returns:
        str: Access token
        
    Raises:
        ProviderAuthError: If auth-service returns 4xx errors (except 429)
        AuthenticationError: If retries are exhausted or unexpected errors occur
    """
    cache_key = f"{firm_id}:{provider}"
    current_time = datetime.now()
    
    # Check cache first
    if cache_key in _token_cache:
        cached_token = _token_cache[cache_key]
        expires_at = cached_token.get("expires_at")
        if expires_at and current_time < expires_at:
            logger.debug(f"Using cached token for firm {firm_id}, provider {provider}")
            return cached_token["access_token"]
        else:
            # Token expired, remove from cache
            del _token_cache[cache_key]
            logger.debug(f"Cached token expired for firm {firm_id}, provider {provider}")
    
    # Fetch from auth-service with retry logic
    try:
        auth_url = f"{settings.auth_service_base}/tokens/{firm_id}/{provider}"
        logger.debug(f"Fetching token from auth-service: {auth_url}")
        
        async with SharedHTTPClient() as client:
            response = await client.get(auth_url)
        
        access_token = response.get("access_token")
        expires_at_timestamp = response.get("expires_at")
        
        if not access_token:
            raise AuthenticationError("No access_token in auth-service response")
        
        # Calculate expiration time (default to 250s TTL if not provided)
        if expires_at_timestamp:
            expires_at = datetime.fromtimestamp(expires_at_timestamp)
        else:
            expires_at = current_time + timedelta(seconds=250)
        
        # Cache the token
        _token_cache[cache_key] = {
            "access_token": access_token,
            "expires_at": expires_at,
            "cached_at": current_time
        }
        
        logger.debug(f"Cached new token for firm {firm_id}, provider {provider}, expires at {expires_at}")
        return access_token
        
    except (AuthRateLimit, AuthUnavailable) as e:
        # These exceptions will trigger retry logic
        _increment_retry_metric(provider)
        logger.warning(f"Retryable auth error for firm {firm_id}, provider {provider}: {e}")
        raise
    except (HTTPNonRetryableError, HTTPRetryableError, HTTPClientError) as e:
        # Map HTTP errors to appropriate auth exceptions
        mapped_error = _map_http_error_to_auth_error(e)
        logger.error(f"Auth error for firm {firm_id}, provider {provider}: {mapped_error}")
        
        if isinstance(mapped_error, (AuthRateLimit, AuthUnavailable)):
            _increment_retry_metric(provider)
            raise mapped_error
        else:
            _increment_failure_metric(provider)
            raise mapped_error
    except Exception as e:
        logger.error(f"Unexpected error fetching token for firm {firm_id}, provider {provider}: {e}")
        _increment_failure_metric(provider)
        
        # If we've exhausted retries, provide user-friendly error message
        if hasattr(e, '__cause__') and isinstance(e.__cause__, (AuthRateLimit, AuthUnavailable)):
            raise ProviderAuthError("Calendar auth service currently unavailable. Please try again later.")
        else:
            raise AuthenticationError(f"Failed to authenticate with {provider}: {e}")


# Legacy function for backward compatibility
async def get_access_token(firm_id: str, provider: str) -> str:
    """
    Legacy function for backward compatibility.
    
    This function is deprecated. Use get_access_token_with_retry instead.
    """
    logger.warning("get_access_token is deprecated, use get_access_token_with_retry instead")
    return await get_access_token_with_retry(firm_id, provider)
