"""
Tests for auth client retry functionality.

This module tests the retry logic for auth-service outages, including
exponential backoff, rate limiting, and metrics collection.
"""

import pytest
import asyncio
import time
from unittest.mock import patch, AsyncMock, MagicMock
from datetime import datetime, timedelta

from shared.auth_client import (
    get_access_token_with_retry,
    _token_cache,
    _increment_retry_metric,
    _increment_failure_metric,
    _map_http_error_to_auth_error
)
from shared.http import HTTPNonRetryableError, HTTPRetryableError, HTTPClientError
from backend.agents.interactive.calendar_crud.providers.exceptions import (
    AuthRateLimit,
    AuthUnavailable,
    ProviderAuthError,
    AuthenticationError
)


@pytest.fixture(autouse=True)
def clear_token_cache():
    """Clear token cache before each test."""
    _token_cache.clear()
    yield
    _token_cache.clear()


@pytest.fixture
def mock_auth_service_base():
    """Mock AUTH_SERVICE_BASE environment variable."""
    with patch('backend.config.settings.auth_service_base', 'http://auth.local'):
        yield


@pytest.fixture
def mock_metrics_collector():
    """Mock metrics collector."""
    mock_collector = MagicMock()
    with patch('shared.auth_client._get_metrics_collector', return_value=mock_collector):
        yield mock_collector


class TestAuthRetryLogic:
    """Test auth retry logic and error handling."""

    @pytest.mark.asyncio
    async def test_successful_token_fetch(self, mock_auth_service_base):
        """Test successful token fetch without retries."""
        mock_response = {
            "access_token": "test_token_123",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        with patch('shared.http.SharedHTTPClient.get', return_value=mock_response):
            token = await get_access_token_with_retry("firm123", "google")
            
            assert token == "test_token_123"
            # Verify token is cached
            cache_key = "firm123:google"
            assert cache_key in _token_cache
            assert _token_cache[cache_key]["access_token"] == "test_token_123"

    @pytest.mark.asyncio
    async def test_cached_token_reuse(self, mock_auth_service_base):
        """Test that cached tokens are reused when not expired."""
        # Pre-populate cache with valid token
        cache_key = "firm123:google"
        future_time = datetime.now() + timedelta(seconds=300)
        _token_cache[cache_key] = {
            "access_token": "cached_token_456",
            "expires_at": future_time,
            "cached_at": datetime.now()
        }
        
        # Should not make HTTP request, use cached token
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            token = await get_access_token_with_retry("firm123", "google")
            
            assert token == "cached_token_456"
            mock_get.assert_not_called()

    @pytest.mark.asyncio
    async def test_expired_token_cache_refresh(self, mock_auth_service_base):
        """Test that expired tokens are refreshed from auth-service."""
        # Pre-populate cache with expired token
        cache_key = "firm123:google"
        past_time = datetime.now() - timedelta(seconds=10)
        _token_cache[cache_key] = {
            "access_token": "expired_token",
            "expires_at": past_time,
            "cached_at": datetime.now() - timedelta(seconds=300)
        }
        
        mock_response = {
            "access_token": "new_token_789",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        with patch('shared.http.SharedHTTPClient.get', return_value=mock_response):
            token = await get_access_token_with_retry("firm123", "google")
            
            assert token == "new_token_789"
            # Verify cache is updated
            assert _token_cache[cache_key]["access_token"] == "new_token_789"

    @pytest.mark.asyncio
    async def test_retry_on_rate_limit_success(self, mock_auth_service_base, mock_metrics_collector):
        """Test retry logic succeeds after rate limit errors."""
        mock_response = {
            "access_token": "success_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        call_count = 0
        async def mock_get_with_retries(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                # First two calls return 429
                raise HTTPNonRetryableError("HTTP 429: Rate limit exceeded")
            else:
                # Third call succeeds
                return mock_response
        
        start_time = time.time()
        with patch('shared.http.SharedHTTPClient.get', side_effect=mock_get_with_retries):
            token = await get_access_token_with_retry("firm123", "google")
            
        end_time = time.time()
        elapsed = end_time - start_time
        
        assert token == "success_token"
        assert call_count == 3
        # Should take at least 1.5s (0.5 + 1.0) for two retries
        assert elapsed >= 1.5
        # Should take less than 8s (max wait time)
        assert elapsed < 8.0
        
        # Verify retry metrics were incremented
        assert mock_metrics_collector.increment_counter.call_count >= 2

    @pytest.mark.asyncio
    async def test_retry_on_server_error_success(self, mock_auth_service_base, mock_metrics_collector):
        """Test retry logic succeeds after 5xx server errors."""
        mock_response = {
            "access_token": "success_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        call_count = 0
        async def mock_get_with_retries(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count <= 2:
                # First two calls return 500
                raise HTTPRetryableError("HTTP 500: Internal server error")
            else:
                # Third call succeeds
                return mock_response
        
        start_time = time.time()
        with patch('shared.http.SharedHTTPClient.get', side_effect=mock_get_with_retries):
            token = await get_access_token_with_retry("firm123", "google")
            
        end_time = time.time()
        elapsed = end_time - start_time
        
        assert token == "success_token"
        assert call_count == 3
        # Should take at least 1.5s for two retries
        assert elapsed >= 1.5
        
        # Verify retry metrics were incremented
        assert mock_metrics_collector.increment_counter.call_count >= 2

    @pytest.mark.asyncio
    async def test_max_retries_exhausted_rate_limit(self, mock_auth_service_base, mock_metrics_collector):
        """Test that ProviderAuthError is raised when retries are exhausted for rate limits."""
        async def mock_get_always_429(*args, **kwargs):
            raise HTTPNonRetryableError("HTTP 429: Rate limit exceeded")
        
        start_time = time.time()
        with patch('shared.http.SharedHTTPClient.get', side_effect=mock_get_always_429):
            with pytest.raises(ProviderAuthError, match="Calendar auth service currently unavailable"):
                await get_access_token_with_retry("firm123", "google")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should take approximately 7.5s (0.5 + 1 + 2 + 4)
        assert elapsed >= 7.0
        assert elapsed < 10.0
        
        # Verify failure metric was incremented
        mock_metrics_collector.increment_counter.assert_called_with(
            "auth_failure_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_max_retries_exhausted_server_error(self, mock_auth_service_base, mock_metrics_collector):
        """Test that ProviderAuthError is raised when retries are exhausted for 5xx errors."""
        async def mock_get_always_500(*args, **kwargs):
            raise HTTPRetryableError("HTTP 500: Internal server error")
        
        with patch('shared.http.SharedHTTPClient.get', side_effect=mock_get_always_500):
            with pytest.raises(ProviderAuthError, match="Calendar auth service currently unavailable"):
                await get_access_token_with_retry("firm123", "google")
        
        # Verify failure metric was incremented
        mock_metrics_collector.increment_counter.assert_called_with(
            "auth_failure_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_no_retry_on_4xx_errors(self, mock_auth_service_base, mock_metrics_collector):
        """Test that 4xx errors (except 429) are not retried."""
        async def mock_get_404(*args, **kwargs):
            raise HTTPNonRetryableError("HTTP 404: Not found")
        
        start_time = time.time()
        with patch('shared.http.SharedHTTPClient.get', side_effect=mock_get_404):
            with pytest.raises(ProviderAuthError, match="Auth service error"):
                await get_access_token_with_retry("firm123", "google")
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should fail immediately without retries
        assert elapsed < 1.0
        
        # Verify failure metric was incremented (no retry metrics)
        mock_metrics_collector.increment_counter.assert_called_once_with(
            "auth_failure_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_no_retry_on_401_errors(self, mock_auth_service_base, mock_metrics_collector):
        """Test that 401 errors are not retried."""
        async def mock_get_401(*args, **kwargs):
            raise HTTPNonRetryableError("HTTP 401: Unauthorized")
        
        with patch('shared.http.SharedHTTPClient.get', side_effect=mock_get_401):
            with pytest.raises(ProviderAuthError, match="Auth service error"):
                await get_access_token_with_retry("firm123", "google")
        
        # Verify failure metric was incremented (no retry metrics)
        mock_metrics_collector.increment_counter.assert_called_once_with(
            "auth_failure_total", tags={"provider": "google"}
        )

    @pytest.mark.asyncio
    async def test_missing_access_token_in_response(self, mock_auth_service_base):
        """Test error handling when access_token is missing from response."""
        mock_response = {"expires_at": int(datetime.now().timestamp())}
        
        with patch('shared.http.SharedHTTPClient.get', return_value=mock_response):
            with pytest.raises(AuthenticationError, match="No access_token in auth-service response"):
                await get_access_token_with_retry("firm123", "google")

    @pytest.mark.asyncio
    async def test_different_providers_separate_cache(self, mock_auth_service_base):
        """Test that different providers have separate cache entries."""
        mock_response_google = {
            "access_token": "google_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        mock_response_calendly = {
            "access_token": "calendly_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        with patch('shared.http.SharedHTTPClient.get') as mock_get:
            # Configure mock to return different responses based on URL
            def side_effect(*args, **kwargs):
                url = args[0] if args else kwargs.get('url', '')
                if 'google' in url:
                    return mock_response_google
                elif 'calendly' in url:
                    return mock_response_calendly
                else:
                    raise ValueError(f"Unexpected URL: {url}")
            
            mock_get.side_effect = side_effect
            
            google_token = await get_access_token_with_retry("firm123", "google")
            calendly_token = await get_access_token_with_retry("firm123", "calendly")
            
            assert google_token == "google_token"
            assert calendly_token == "calendly_token"
            
            # Verify separate cache entries
            assert "firm123:google" in _token_cache
            assert "firm123:calendly" in _token_cache
            assert _token_cache["firm123:google"]["access_token"] == "google_token"
            assert _token_cache["firm123:calendly"]["access_token"] == "calendly_token"


class TestErrorMapping:
    """Test HTTP error to auth error mapping."""

    def test_map_429_to_rate_limit(self):
        """Test that 429 errors are mapped to AuthRateLimit."""
        http_error = HTTPNonRetryableError("HTTP 429: Rate limit exceeded")
        auth_error = _map_http_error_to_auth_error(http_error)
        
        assert isinstance(auth_error, AuthRateLimit)
        assert "Auth service rate limit" in str(auth_error)

    def test_map_4xx_to_provider_auth_error(self):
        """Test that other 4xx errors are mapped to ProviderAuthError."""
        http_error = HTTPNonRetryableError("HTTP 404: Not found")
        auth_error = _map_http_error_to_auth_error(http_error)
        
        assert isinstance(auth_error, ProviderAuthError)
        assert "Auth service error" in str(auth_error)

    def test_map_5xx_to_auth_unavailable(self):
        """Test that 5xx errors are mapped to AuthUnavailable."""
        http_error = HTTPRetryableError("HTTP 500: Internal server error")
        auth_error = _map_http_error_to_auth_error(http_error)
        
        assert isinstance(auth_error, AuthUnavailable)
        assert "Auth service unavailable" in str(auth_error)

    def test_map_network_error_to_auth_unavailable(self):
        """Test that network errors are mapped to AuthUnavailable."""
        http_error = HTTPClientError("Connection timeout")
        auth_error = _map_http_error_to_auth_error(http_error)
        
        assert isinstance(auth_error, AuthUnavailable)
        assert "Auth service unavailable" in str(auth_error)

    def test_map_unknown_error_to_auth_error(self):
        """Test that unknown errors are mapped to AuthenticationError."""
        unknown_error = ValueError("Unknown error")
        auth_error = _map_http_error_to_auth_error(unknown_error)
        
        assert isinstance(auth_error, AuthenticationError)
        assert "Unexpected auth error" in str(auth_error)


class TestMetrics:
    """Test metrics collection for auth retry logic."""

    def test_increment_retry_metric(self, mock_metrics_collector):
        """Test that retry metrics are incremented correctly."""
        _increment_retry_metric("google")
        
        mock_metrics_collector.increment_counter.assert_called_once_with(
            "auth_retry_total", tags={"provider": "google"}
        )

    def test_increment_failure_metric(self, mock_metrics_collector):
        """Test that failure metrics are incremented correctly."""
        _increment_failure_metric("calendly")
        
        mock_metrics_collector.increment_counter.assert_called_once_with(
            "auth_failure_total", tags={"provider": "calendly"}
        )

    def test_metrics_collector_unavailable(self):
        """Test graceful handling when metrics collector is unavailable."""
        with patch('shared.auth_client._get_metrics_collector', return_value=None):
            # Should not raise any exceptions
            _increment_retry_metric("google")
            _increment_failure_metric("google")


class TestIntegration:
    """Integration tests with actual provider clients."""

    @pytest.mark.asyncio
    async def test_google_client_uses_retry_wrapper(self, mock_auth_service_base):
        """Test that Google client uses the retry wrapper."""
        from backend.agents.interactive.calendar_crud.providers.google.client import GoogleCalendarClient
        
        mock_response = {
            "access_token": "google_integration_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        with patch('shared.auth_client.get_access_token_with_retry', return_value="google_integration_token") as mock_retry:
            client = GoogleCalendarClient()
            token = await client._get_token("firm123")
            
            assert token == "google_integration_token"
            mock_retry.assert_called_once_with("firm123", "google")

    @pytest.mark.asyncio
    async def test_calendly_client_uses_retry_wrapper(self, mock_auth_service_base):
        """Test that Calendly client uses the retry wrapper."""
        from backend.agents.interactive.calendar_crud.providers.calendly.client import CalendlyClient
        
        mock_response = {
            "access_token": "calendly_integration_token",
            "expires_at": int((datetime.now() + timedelta(seconds=300)).timestamp())
        }
        
        with patch('shared.auth_client.get_access_token_with_retry', return_value="calendly_integration_token") as mock_retry:
            client = CalendlyClient()
            token = await client._get_token("firm123")
            
            assert token == "calendly_integration_token"
            mock_retry.assert_called_once_with("firm123", "calendly")
